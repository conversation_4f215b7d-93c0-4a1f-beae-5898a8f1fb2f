/**
 * 简单的Markdown解析器
 * 将Markdown文本解析为可渲染的数据结构
 */

/**
 * 解析Markdown内容为渲染数据
 * @param {string} markdown - Markdown文本
 * @returns {Array} 解析后的段落数组
 */
export function parseMarkdown(markdown) {
  if (!markdown || typeof markdown !== 'string') {
    return []
  }

  const lines = markdown.split('\n')
  const result = []
  let currentList = null
  let listType = null

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    
    // 跳过空行
    if (!line) {
      // 如果当前在处理列表，空行结束列表
      if (currentList) {
        result.push({
          type: 'list',
          listType: listType,
          items: currentList
        })
        currentList = null
        listType = null
      }
      continue
    }

    // 解析标题 (# ## ###)
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
    if (headerMatch) {
      // 结束当前列表
      if (currentList) {
        result.push({
          type: 'list',
          listType: listType,
          items: currentList
        })
        currentList = null
        listType = null
      }

      const level = headerMatch[1].length
      const text = headerMatch[2]
      result.push({
        type: 'header',
        level: level,
        text: text
      })
      continue
    }

    // 解析无序列表 (- * +)
    const unorderedListMatch = line.match(/^[-*+]\s+(.+)$/)
    if (unorderedListMatch) {
      const text = unorderedListMatch[1]
      
      // 如果当前不是无序列表，开始新列表
      if (!currentList || listType !== 'unordered') {
        if (currentList) {
          result.push({
            type: 'list',
            listType: listType,
            items: currentList
          })
        }
        currentList = []
        listType = 'unordered'
      }
      
      currentList.push({
        text: parseInlineMarkdown(text)
      })
      continue
    }

    // 解析有序列表 (1. 2. 3.)
    const orderedListMatch = line.match(/^\d+\.\s+(.+)$/)
    if (orderedListMatch) {
      const text = orderedListMatch[1]
      
      // 如果当前不是有序列表，开始新列表
      if (!currentList || listType !== 'ordered') {
        if (currentList) {
          result.push({
            type: 'list',
            listType: listType,
            items: currentList
          })
        }
        currentList = []
        listType = 'ordered'
      }
      
      currentList.push({
        text: parseInlineMarkdown(text)
      })
      continue
    }

    // 解析引用 (>)
    const quoteMatch = line.match(/^>\s+(.+)$/)
    if (quoteMatch) {
      // 结束当前列表
      if (currentList) {
        result.push({
          type: 'list',
          listType: listType,
          items: currentList
        })
        currentList = null
        listType = null
      }

      const text = quoteMatch[1]
      result.push({
        type: 'quote',
        text: parseInlineMarkdown(text)
      })
      continue
    }

    // 解析代码块 (```)
    if (line.startsWith('```')) {
      // 结束当前列表
      if (currentList) {
        result.push({
          type: 'list',
          listType: listType,
          items: currentList
        })
        currentList = null
        listType = null
      }

      const language = line.substring(3).trim()
      const codeLines = []
      
      // 查找代码块结束
      i++
      while (i < lines.length && !lines[i].trim().startsWith('```')) {
        codeLines.push(lines[i])
        i++
      }
      
      result.push({
        type: 'code',
        language: language,
        text: codeLines.join('\n')
      })
      continue
    }

    // 解析分割线 (--- 或 ***)
    if (line.match(/^(-{3,}|\*{3,})$/)) {
      // 结束当前列表
      if (currentList) {
        result.push({
          type: 'list',
          listType: listType,
          items: currentList
        })
        currentList = null
        listType = null
      }

      result.push({
        type: 'divider'
      })
      continue
    }

    // 普通段落
    if (currentList) {
      result.push({
        type: 'list',
        listType: listType,
        items: currentList
      })
      currentList = null
      listType = null
    }

    result.push({
      type: 'paragraph',
      text: parseInlineMarkdown(line)
    })
  }

  // 处理最后的列表
  if (currentList) {
    result.push({
      type: 'list',
      listType: listType,
      items: currentList
    })
  }

  return result
}

/**
 * 解析行内Markdown格式
 * @param {string} text - 文本内容
 * @returns {Array} 解析后的文本片段数组
 */
function parseInlineMarkdown(text) {
  const result = []
  let currentText = text
  let index = 0

  while (index < currentText.length) {
    // 查找下一个格式标记
    const boldMatch = currentText.substring(index).match(/\*\*([^*]+)\*\*/)
    const italicMatch = currentText.substring(index).match(/\*([^*]+)\*/)
    const codeMatch = currentText.substring(index).match(/`([^`]+)`/)
    const linkMatch = currentText.substring(index).match(/\[([^\]]+)\]\(([^)]+)\)/)

    // 找到最近的格式标记
    let nearestMatch = null
    let nearestIndex = currentText.length
    let matchType = null

    if (boldMatch && boldMatch.index + index < nearestIndex) {
      nearestMatch = boldMatch
      nearestIndex = boldMatch.index + index
      matchType = 'bold'
    }

    if (italicMatch && italicMatch.index + index < nearestIndex && italicMatch.index + index !== nearestIndex) {
      nearestMatch = italicMatch
      nearestIndex = italicMatch.index + index
      matchType = 'italic'
    }

    if (codeMatch && codeMatch.index + index < nearestIndex) {
      nearestMatch = codeMatch
      nearestIndex = codeMatch.index + index
      matchType = 'code'
    }

    if (linkMatch && linkMatch.index + index < nearestIndex) {
      nearestMatch = linkMatch
      nearestIndex = linkMatch.index + index
      matchType = 'link'
    }

    if (nearestMatch) {
      // 添加格式标记前的普通文本
      if (nearestIndex > index) {
        const plainText = currentText.substring(index, nearestIndex)
        if (plainText) {
          result.push({
            type: 'text',
            text: plainText
          })
        }
      }

      // 添加格式化文本
      switch (matchType) {
        case 'bold':
          result.push({
            type: 'bold',
            text: nearestMatch[1]
          })
          index = nearestIndex + nearestMatch[0].length
          break
        case 'italic':
          result.push({
            type: 'italic',
            text: nearestMatch[1]
          })
          index = nearestIndex + nearestMatch[0].length
          break
        case 'code':
          result.push({
            type: 'inline-code',
            text: nearestMatch[1]
          })
          index = nearestIndex + nearestMatch[0].length
          break
        case 'link':
          result.push({
            type: 'link',
            text: nearestMatch[1],
            url: nearestMatch[2]
          })
          index = nearestIndex + nearestMatch[0].length
          break
      }
    } else {
      // 没有更多格式标记，添加剩余文本
      const remainingText = currentText.substring(index)
      if (remainingText) {
        result.push({
          type: 'text',
          text: remainingText
        })
      }
      break
    }
  }

  return result.length > 0 ? result : [{ type: 'text', text: text }]
}
